# 云链IoT平台工程架构图

## 项目概述

云链IoT平台（cloud-link-iot）是基于Spring Boot 3.2 + Spring Cloud微服务架构的企业级物联网管理平台，支持多协议设备接入、实时数据处理、规则引擎、设备管理等完整的IoT解决方案。

## 系统架构总览

```mermaid
graph TB
    subgraph "客户端层"
        WebUI[管理控制台<br/>Web界面]
        MobileApp[移动应用<br/>App/小程序]
        APIClient[第三方系统<br/>API调用]
    end

    subgraph "云端IoT平台"
        subgraph "cloud-iot-gateway 网关接入层"
            Gateway[IoT网关服务<br/>IotGatewayServerApplication]
            HttpProtocol[HTTP协议处理器<br/>IotHttpUpstreamProtocol]
            MqttProtocol[MQTT协议处理器<br/>IotEmqxUpstreamProtocol] 
            TcpProtocol[TCP协议处理器<br/>IotTcpUpstreamProtocol]
            AuthService[设备认证服务<br/>IotEmqxAuthEventProtocol]
        end
        
        subgraph "cloud-iot-api RPC接口层"
            DeviceAPI[设备管理API<br/>@FeignClient]
            ProductAPI[产品管理API<br/>@FeignClient]
            AuthAPI[认证授权API<br/>@FeignClient]
        end
        
        subgraph "cloud-iot-biz 核心业务层"
            MainApp[主应用服务<br/>IotServerApplication]
            
            subgraph "业务服务"
                DeviceService[设备管理服务<br/>DeviceService]
                ProductService[产品管理服务<br/>ProductService]
                IndexService[统计分析服务<br/>IndexService]
                TransportService[数据转发服务<br/>TransportRuleService]
            end
            
            subgraph "消息处理"
                CommandReceiver[命令接收器<br/>@RocketMQMessageListener]
                MqttPublisher[MQTT发布器<br/>@Service]
                MessageProcessor[消息处理器]
            end
            
            subgraph "规则引擎"
                RuleEngine[场景规则引擎<br/>SceneRuleService]
                RuleAction[规则动作执行<br/>SceneRuleActionService]
                RuleTrigger[规则触发器<br/>SceneRuleTriggerService]
            end
        end
        
        subgraph "消息中间件"
            RocketMQ[RocketMQ消息队列<br/>异步消息处理]
            Redis[Redis缓存<br/>会话/配置缓存]
        end
        
        subgraph "数据存储层"
            MySQL[(MySQL数据库<br/>业务数据存储)]
            TDengine[(TDengine时序数据库<br/>设备数据存储)]
        end
    end
    
    subgraph "设备端"
        IoTDevice[IoT终端设备<br/>传感器/执行器]
        EdgeGateway[边缘网关<br/>协议转换/数据聚合]
        DirectDevice[直连设备<br/>支持多协议]
    end
    
    subgraph "外部服务"
        NacosRegistry[Nacos注册中心<br/>服务发现]
        NacosConfig[Nacos配置中心<br/>配置管理]
        SystemService[系统服务<br/>用户权限管理]
        ThirdParty[第三方系统<br/>数据对接]
    end

    %% 设备接入数据流
    IoTDevice -->|"MQTT/HTTP/TCP<br/>数据上报"| Gateway
    EdgeGateway -->|"批量数据<br/>协议转换"| Gateway
    DirectDevice -->|"直连通信<br/>实时数据"| Gateway
    
    Gateway -->|"协议适配"| HttpProtocol
    Gateway -->|"协议适配"| MqttProtocol
    Gateway -->|"协议适配"| TcpProtocol
    
    HttpProtocol -->|"设备认证"| AuthService
    MqttProtocol -->|"设备认证"| AuthService
    TcpProtocol -->|"设备认证"| AuthService
    
    AuthService -->|"消息路由"| RocketMQ
    
    %% 业务处理流程
    RocketMQ -->|"消息消费"| CommandReceiver
    CommandReceiver -->|"数据解析"| MessageProcessor
    MessageProcessor -->|"业务处理"| DeviceService
    MessageProcessor -->|"规则匹配"| RuleEngine
    
    RuleEngine -->|"触发条件"| RuleTrigger
    RuleEngine -->|"执行动作"| RuleAction
    RuleAction -->|"设备控制"| MqttPublisher
    RuleAction -->|"数据转发"| TransportService
    
    %% 管理后台流程
    WebUI -->|"管理操作"| DeviceAPI
    WebUI -->|"管理操作"| ProductAPI
    WebUI -->|"管理操作"| AuthAPI
    MobileApp -->|"移动管理"| DeviceAPI
    APIClient -->|"第三方调用"| DeviceAPI
    
    DeviceAPI -->|"RPC调用"| DeviceService
    ProductAPI -->|"RPC调用"| ProductService
    AuthAPI -->|"RPC调用"| AuthService
    
    %% 数据存储
    DeviceService -.->|"设备信息"| MySQL
    ProductService -.->|"产品配置"| MySQL
    MessageProcessor -.->|"时序数据"| TDengine
    IndexService -.->|"统计数据"| TDengine
    
    %% 缓存使用
    DeviceService -.->|"设备状态"| Redis
    AuthService -.->|"认证会话"| Redis
    RuleEngine -.->|"规则缓存"| Redis
    
    %% 外部依赖
    Gateway -.->|"服务注册"| NacosRegistry
    MainApp -.->|"配置获取"| NacosConfig
    AuthService -.->|"用户验证"| SystemService
    
    %% 控制指令下发
    MqttPublisher -->|"指令下发"| Gateway
    Gateway -->|"控制指令"| IoTDevice
    TransportService -->|"数据推送"| ThirdParty

    %% 样式定义
    classDef clientClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef gatewayClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef apiClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef businessClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef messageClass fill:#f1f8e9,stroke:#689f38,stroke-width:2px
    classDef dataClass fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef deviceClass fill:#e0f2f1,stroke:#00796b,stroke-width:2px
    classDef externalClass fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px

    class WebUI,MobileApp,APIClient clientClass
    class Gateway,HttpProtocol,MqttProtocol,TcpProtocol,AuthService gatewayClass
    class DeviceAPI,ProductAPI,AuthAPI apiClass
    class MainApp,DeviceService,ProductService,IndexService,TransportService,CommandReceiver,MqttPublisher,MessageProcessor,RuleEngine,RuleAction,RuleTrigger businessClass
    class RocketMQ,Redis messageClass
    class MySQL,TDengine dataClass
    class IoTDevice,EdgeGateway,DirectDevice deviceClass
    class NacosRegistry,NacosConfig,SystemService,ThirdParty externalClass
```

## 详细技术架构

```mermaid
graph TB
    subgraph "技术架构分层"
        subgraph "表现层 Presentation Layer"
            UI[前端界面<br/>React/Vue.js]
            API_Gateway[API网关<br/>Spring Cloud Gateway]
            RestAPI[RESTful API<br/>Spring MVC]
        end
        
        subgraph "服务层 Service Layer"
            BizService[业务服务<br/>@Service组件]
            RPC_Client[RPC客户端<br/>OpenFeign]
            MessageQueue[消息队列<br/>RocketMQ]
            Cache[缓存服务<br/>Redis]
        end
        
        subgraph "应用层 Application Layer"
            SpringBoot[Spring Boot 3.2<br/>应用容器]
            Security[Spring Security<br/>安全框架]
            Validation[参数校验<br/>Hibernate Validator]
        end
        
        subgraph "基础设施层 Infrastructure Layer"
            Database[数据持久化<br/>MyBatis-Plus]
            ConfigCenter[配置中心<br/>Nacos Config]
            Registry[注册中心<br/>Nacos Discovery]
            Monitoring[监控告警<br/>Spring Actuator]
        end
        
        subgraph "数据层 Data Layer"
            RelationalDB[(关系数据库<br/>MySQL 8.0)]
            TimeSeriesDB[(时序数据库<br/>TDengine)]
            FileStorage[(文件存储<br/>本地/OSS)]
        end
    end

    %% 层级关系
    UI --> RestAPI
    API_Gateway --> RestAPI
    RestAPI --> BizService
    BizService --> RPC_Client
    BizService --> MessageQueue
    BizService --> Cache
    
    BizService --> SpringBoot
    SpringBoot --> Security
    SpringBoot --> Validation
    
    SpringBoot --> Database
    SpringBoot --> ConfigCenter
    SpringBoot --> Registry
    SpringBoot --> Monitoring
    
    Database --> RelationalDB
    Database --> TimeSeriesDB
    Database --> FileStorage

    classDef layerClass fill:#f0f4f8,stroke:#2d3748,stroke-width:2px
    classDef componentClass fill:#edf2f7,stroke:#4a5568,stroke-width:1px
```

## 模块详细说明

### 🌐 cloud-iot-gateway (网关接入层)

**主要功能:**
- 多协议设备接入支持（HTTP、MQTT、TCP）
- 设备身份认证和授权
- 协议适配和消息转换
- 连接状态管理
- 消息路由和转发

**核心组件:**
- `IotGatewayServerApplication`: 网关服务启动类
- `IotHttpUpstreamProtocol`: HTTP协议处理器
- `IotEmqxUpstreamProtocol`: MQTT协议处理器  
- `IotTcpUpstreamProtocol`: TCP协议处理器
- `IotEmqxAuthEventProtocol`: 设备认证处理器

### 💼 cloud-iot-biz (核心业务层)

**主要功能:**
- 设备生命周期管理
- 产品模型定义和管理
- 规则引擎和场景联动
- 数据处理和转发
- 统计分析和监控

**核心服务:**
- `DeviceService`: 设备管理服务
- `ProductService`: 产品管理服务
- `SceneRuleService`: 规则引擎服务
- `TransportRuleService`: 数据转发服务
- `IndexService`: 统计分析服务

**消息处理:**
- `CommandReceiver`: RocketMQ消息消费者
- `MqttPublisher`: MQTT消息发布器

### 🔌 cloud-iot-api (接口定义层)

**主要功能:**
- RPC接口定义
- Feign客户端配置
- API文档生成
- 数据模型定义

## 核心业务流程

### 1️⃣ 设备接入认证流程

```mermaid
sequenceDiagram
    participant Device as IoT设备
    participant Gateway as 网关服务
    participant Auth as 认证服务
    participant Redis as Redis缓存
    participant DeviceService as 设备服务
    
    Device->>Gateway: 1.建立连接(MQTT/HTTP/TCP)
    Gateway->>Auth: 2.设备认证请求
    Auth->>DeviceService: 3.验证设备凭证
    DeviceService->>Redis: 4.检查设备状态
    Redis-->>DeviceService: 5.返回设备信息
    DeviceService-->>Auth: 6.认证结果
    Auth-->>Gateway: 7.认证成功/失败
    Gateway-->>Device: 8.连接确认
    Gateway->>Redis: 9.更新在线状态
```

### 2️⃣ 数据上报处理流程

```mermaid
sequenceDiagram
    participant Device as IoT设备
    participant Gateway as 网关服务
    participant RocketMQ as 消息队列
    participant CommandReceiver as 消息消费者
    participant RuleEngine as 规则引擎
    participant TDengine as 时序数据库
    
    Device->>Gateway: 1.上报设备数据
    Gateway->>RocketMQ: 2.发送消息到队列
    RocketMQ->>CommandReceiver: 3.消费消息
    CommandReceiver->>RuleEngine: 4.触发规则匹配
    CommandReceiver->>TDengine: 5.存储时序数据
    RuleEngine->>Gateway: 6.执行控制动作
    Gateway->>Device: 7.下发控制指令
```

### 3️⃣ 管理后台操作流程

```mermaid
sequenceDiagram
    participant Admin as 管理员
    participant WebUI as Web界面
    participant API as Feign接口
    participant BizService as 业务服务
    participant MySQL as MySQL数据库
    
    Admin->>WebUI: 1.登录系统
    WebUI->>API: 2.调用管理接口
    API->>BizService: 3.RPC服务调用
    BizService->>MySQL: 4.数据库操作
    MySQL-->>BizService: 5.返回结果
    BizService-->>API: 6.业务处理结果
    API-->>WebUI: 7.接口响应
    WebUI-->>Admin: 8.界面更新
```

## 技术栈详解

### 🏗️ 核心框架
- **Spring Boot**: 3.2.0 - 应用基础框架
- **Spring Cloud**: 微服务组件 - 服务治理
- **Spring Security**: 安全认证授权
- **Spring MVC**: Web层框架

### 📨 消息通信  
- **RocketMQ**: 消息队列中间件
- **MQTT**: Eclipse Paho客户端
- **OpenFeign**: 声明式RPC调用
- **HTTP**: RESTful API通信

### 💾 数据存储
- **MySQL**: 8.0+ 关系型数据库
- **TDengine**: 时序数据库
- **Redis**: 缓存和会话存储
- **MyBatis-Plus**: ORM持久化框架

### 🔧 开发工具
- **Maven**: 项目构建管理
- **Lombok**: 代码简化工具
- **MapStruct**: 对象映射框架
- **Jackson**: JSON序列化框架

### ☁️ 微服务治理
- **Nacos**: 注册中心和配置中心
- **Spring Cloud Gateway**: API网关
- **Spring Boot Actuator**: 健康检查
- **Swagger**: API文档生成

## 数据流向说明

### 📈 上行数据流 (设备→云端)
1. **设备连接**: IoT设备通过MQTT/HTTP/TCP协议连接网关
2. **协议解析**: 网关根据协议类型进行消息解析和验证
3. **身份认证**: 验证设备身份，生成会话令牌
4. **消息路由**: 将设备数据发送到RocketMQ消息队列
5. **异步处理**: CommandReceiver消费消息进行业务处理
6. **数据存储**: 时序数据存储到TDengine，配置数据存储到MySQL
7. **规则匹配**: 触发规则引擎进行场景联动和告警处理

### 📉 下行数据流 (云端→设备)
1. **指令发起**: 管理后台或规则引擎发起设备控制指令
2. **API调用**: 通过Feign接口调用业务服务
3. **消息发布**: MqttPublisher发布控制消息到消息队列
4. **网关转发**: 网关接收并根据协议转发指令到目标设备
5. **设备执行**: 设备接收指令并执行相应的控制动作
6. **结果反馈**: 设备将执行结果通过上行数据流反馈给云端

## 部署架构建议

### 🏭 生产环境部署
```
负载均衡器 (Nginx/ALB)
    ↓
API网关集群 (Spring Cloud Gateway)
    ↓
├─ 网关服务集群 (cloud-iot-gateway)
├─ 业务服务集群 (cloud-iot-biz)  
└─ 接口服务集群 (cloud-iot-api)
    ↓
├─ 消息队列集群 (RocketMQ)
├─ 缓存集群 (Redis Cluster)
├─ 数据库主从 (MySQL Master-Slave)
└─ 时序数据库 (TDengine Cluster)
```

### 🛠️ 开发环境部署
- 单机部署所有服务组件
- 使用内嵌数据库进行快速开发
- 本地Nacos配置简化开发流程
- Docker Compose一键启动环境

## 系统特性

### 🚀 高性能特性
- **异步消息处理**: 基于RocketMQ的高吞吐量消息处理
- **多级缓存**: Redis缓存提升数据访问性能
- **时序数据优化**: TDengine针对IoT数据的高效存储
- **连接池管理**: 数据库和Redis连接池优化

### 🔒 安全可靠特性
- **多层认证**: 设备认证 + 用户认证双重保障
- **JWT令牌**: 无状态令牌机制保证安全性
- **权限控制**: 基于角色的细粒度权限管理
- **数据加密**: 敏感数据传输和存储加密

### 📊 可观测性特性
- **实时监控**: 设备在线状态和数据流监控
- **统计分析**: 多维度数据统计和趋势分析
- **健康检查**: 服务健康状态自动检测
- **链路追踪**: 分布式请求链路跟踪

### 🎯 扩展性特性
- **微服务架构**: 水平扩展和服务解耦
- **插件化规则**: 灵活的规则引擎配置
- **多协议支持**: 易于扩展新的设备接入协议
- **API开放**: 标准化接口支持第三方集成

---

**文档版本**: v1.0  
**更新时间**: 2024年12月  
**维护团队**: 云链IoT平台开发团队

*本文档详细描述了云链IoT平台的完整架构设计，为开发、测试、运维团队提供技术参考。* 